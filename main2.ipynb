! pip install llama-index

! pip install llama-index-embeddings-fastembed

! pip install fastembed

! pip install llama-index-vector-stores-faiss

! pip install llama-index-llms-groq

from llama_index.embeddings.fastembed import FastEmbedEmbedding
from llama_index.core import Settings

Settings.embed_model = FastEmbedEmbedding(
    model_name="BAAI/bge-small-en-v1.5",
    embed_batch_size=32
)

import faiss

# dimension for BAAI/bge-small-en-v1.5
d = 384
faiss_index = faiss.IndexFlatL2(d)

from llama_index.vector_stores.faiss import FaissVectorStore

from llama_index.core import VectorStoreIndex, StorageContext

from llama_index.core import load_index_from_storage

# load index from disk
vector_store = FaissVectorStore.from_persist_dir("faiss_db")
storage_context = StorageContext.from_defaults(
    vector_store=vector_store, persist_dir="faiss_db"
)

index = load_index_from_storage(storage_context=storage_context,index_id="2a3e044a-5744-41d0-9873-8d679b1571a8")


from llama_index.llms.groq import Groq

llm = Groq(model="llama-3.1-8b-instant", api_key="********************************************************")  

from llama_index.core.prompts import PromptTemplate

# Define your custom prompt
ayurveda_prompt_str = """You are an expert Ayurvedic physician. 

CRITICAL: Focus ONLY on what the user is currently asking about. If they ask about Apigenin, discuss Apigenin. If they ask about diabetes, discuss diabetes. DO NOT continue previous topics unless explicitly asked.

Context from knowledge base: {context_str}

Current Question: {query_str}

Provide a response following this structure when applicable:

**1. Definition/Information**
- What is it from an Ayurvedic perspective
- Key properties and characteristics
- Causes what is it caused by
- Symptoms if it's a condition

**2. Ayurvedic Treatment/Remedies** (if applicable)
- Herbal medicines with specific dosages
- Dietary recommendations
- Lifestyle modifications
- Yoga/pranayama practices
- Panchakarma therapies

**3. Benefits/Expected Outcomes**
- How it works in the body
- Expected results and timeline
- Scientific backing if available

Guidelines:
- Answer ONLY about the specific topic asked
- Be direct - no greetings or introductions
- Use bullet points for clarity
- Include Sanskrit names with translations
- Provide practical, actionable advice
- If asked about a compound/herb, focus on its properties and uses
- If the topic doesn't fit the structure, adapt accordingly
- End with healthcare consultation reminder for medical conditions

Remember: Each question is independent. Do not reference or continue from previous answers unless the user explicitly asks for elaboration."""

ayurveda_prompt = PromptTemplate(ayurveda_prompt_str)

# Pass the prompt to the query engine
query_engine = index.as_query_engine(
    llm=llm,
    similarity_top_k=3,
    text_qa_template=ayurveda_prompt
)


response = query_engine.query("What are the best remnedy for  Indigestion ")
print(response)

