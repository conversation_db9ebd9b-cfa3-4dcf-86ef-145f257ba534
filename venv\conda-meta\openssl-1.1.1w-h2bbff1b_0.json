{"build": "h2bbff1b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["ca-certificates", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\openssl-1.1.1w-h2bbff1b_0", "features": "", "files": ["Library/bin/c_rehash.pl", "Library/bin/libcrypto-1_1-x64.dll", "Library/bin/libcrypto-1_1-x64.pdb", "Library/bin/libssl-1_1-x64.dll", "Library/bin/libssl-1_1-x64.pdb", "Library/bin/openssl.exe", "Library/bin/openssl.pdb", "Library/include/openssl/__DECC_INCLUDE_EPILOGUE.H", "Library/include/openssl/__DECC_INCLUDE_PROLOGUE.H", "Library/include/openssl/aes.h", "Library/include/openssl/applink.c", "Library/include/openssl/asn1.h", "Library/include/openssl/asn1_mac.h", "Library/include/openssl/asn1err.h", "Library/include/openssl/asn1t.h", "Library/include/openssl/async.h", "Library/include/openssl/asyncerr.h", "Library/include/openssl/bio.h", "Library/include/openssl/bioerr.h", "Library/include/openssl/blowfish.h", "Library/include/openssl/bn.h", "Library/include/openssl/bnerr.h", "Library/include/openssl/buffer.h", "Library/include/openssl/buffererr.h", "Library/include/openssl/camellia.h", "Library/include/openssl/cast.h", "Library/include/openssl/cmac.h", "Library/include/openssl/cms.h", "Library/include/openssl/cmserr.h", "Library/include/openssl/comp.h", "Library/include/openssl/comperr.h", "Library/include/openssl/conf.h", "Library/include/openssl/conf_api.h", "Library/include/openssl/conferr.h", "Library/include/openssl/crypto.h", "Library/include/openssl/cryptoerr.h", "Library/include/openssl/ct.h", "Library/include/openssl/cterr.h", "Library/include/openssl/des.h", "Library/include/openssl/dh.h", "Library/include/openssl/dherr.h", "Library/include/openssl/dsa.h", "Library/include/openssl/dsaerr.h", "Library/include/openssl/dtls1.h", "Library/include/openssl/e_os2.h", "Library/include/openssl/ebcdic.h", "Library/include/openssl/ec.h", "Library/include/openssl/ecdh.h", "Library/include/openssl/ecdsa.h", "Library/include/openssl/ecerr.h", "Library/include/openssl/engine.h", "Library/include/openssl/engineerr.h", "Library/include/openssl/err.h", "Library/include/openssl/evp.h", "Library/include/openssl/evperr.h", "Library/include/openssl/hmac.h", "Library/include/openssl/idea.h", "Library/include/openssl/kdf.h", "Library/include/openssl/kdferr.h", "Library/include/openssl/lhash.h", "Library/include/openssl/md2.h", "Library/include/openssl/md4.h", "Library/include/openssl/md5.h", "Library/include/openssl/mdc2.h", "Library/include/openssl/modes.h", "Library/include/openssl/obj_mac.h", "Library/include/openssl/objects.h", "Library/include/openssl/objectserr.h", "Library/include/openssl/ocsp.h", "Library/include/openssl/ocsperr.h", "Library/include/openssl/opensslconf.h", "Library/include/openssl/opensslv.h", "Library/include/openssl/ossl_typ.h", "Library/include/openssl/pem.h", "Library/include/openssl/pem2.h", "Library/include/openssl/pemerr.h", "Library/include/openssl/pkcs12.h", "Library/include/openssl/pkcs12err.h", "Library/include/openssl/pkcs7.h", "Library/include/openssl/pkcs7err.h", "Library/include/openssl/rand.h", "Library/include/openssl/rand_drbg.h", "Library/include/openssl/randerr.h", "Library/include/openssl/rc2.h", "Library/include/openssl/rc4.h", "Library/include/openssl/rc5.h", "Library/include/openssl/ripemd.h", "Library/include/openssl/rsa.h", "Library/include/openssl/rsaerr.h", "Library/include/openssl/safestack.h", "Library/include/openssl/seed.h", "Library/include/openssl/sha.h", "Library/include/openssl/srp.h", "Library/include/openssl/srtp.h", "Library/include/openssl/ssl.h", "Library/include/openssl/ssl2.h", "Library/include/openssl/ssl3.h", "Library/include/openssl/sslerr.h", "Library/include/openssl/stack.h", "Library/include/openssl/store.h", "Library/include/openssl/storeerr.h", "Library/include/openssl/symhacks.h", "Library/include/openssl/tls1.h", "Library/include/openssl/ts.h", "Library/include/openssl/tserr.h", "Library/include/openssl/txt_db.h", "Library/include/openssl/ui.h", "Library/include/openssl/uierr.h", "Library/include/openssl/whrlpool.h", "Library/include/openssl/x509.h", "Library/include/openssl/x509_vfy.h", "Library/include/openssl/x509err.h", "Library/include/openssl/x509v3.h", "Library/include/openssl/x509v3err.h", "Library/lib/engines-1_1/capi.dll", "Library/lib/engines-1_1/capi.pdb", "Library/lib/engines-1_1/padlock.dll", "Library/lib/engines-1_1/padlock.pdb", "Library/lib/libcrypto.lib", "Library/lib/libssl.lib", "Library/ssl/ct_log_list.cnf", "Library/ssl/ct_log_list.cnf.dist", "Library/ssl/misc/CA.pl", "Library/ssl/misc/tsget.pl", "Library/ssl/openssl.cnf", "Library/ssl/openssl.cnf.dist", ".nonadmin"], "fn": "openssl-1.1.1w-h2bbff1b_0.conda", "legacy_bz2_md5": "a67d4a763641de641ea9dfa8c4a5f567", "legacy_bz2_size": 6048217, "license": "OpenSSL", "license_family": "Apache", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\openssl-1.1.1w-h2bbff1b_0", "type": 1}, "md5": "e2e24f1ea50292279c7ea3fe32924095", "name": "openssl", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\openssl-1.1.1w-h2bbff1b_0", "paths_data": {"paths": [{"_path": "Library/bin/c_rehash.pl", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\\\b\\\\abs_12_dc77dam\\\\croot\\\\openssl_1694464964882\\\\_h_env", "sha256": "cf3efc4498dd9e89badbc03fb03c4a2a6b64e1eeabebc8f1cca6307d63267ebb", "sha256_in_prefix": "de8322d374cd0e82353dde5b314de4945b0debefaa6ab49aea4576b15f0ea887", "size_in_bytes": 7104}, {"_path": "Library/bin/libcrypto-1_1-x64.dll", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\b\\abs_12_dc77dam\\croot\\openssl_1694464964882\\_h_env", "sha256": "73296b6ee04fa23a8e42e312d42728b392c8b5b5f2a2169eb079de7f6a611e69", "sha256_in_prefix": "73296b6ee04fa23a8e42e312d42728b392c8b5b5f2a2169eb079de7f6a611e69", "size_in_bytes": 3412992}, {"_path": "Library/bin/libcrypto-1_1-x64.pdb", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\\\b\\\\abs_12_dc77dam\\\\croot\\\\openssl_1694464964882\\\\_h_env", "sha256": "0c2c5473c808e5cdbc58d0847b2fffdc0610b0d5b85ff89fbd7425682acca987", "sha256_in_prefix": "0c2c5473c808e5cdbc58d0847b2fffdc0610b0d5b85ff89fbd7425682acca987", "size_in_bytes": 10227712}, {"_path": "Library/bin/libssl-1_1-x64.dll", "path_type": "hardlink", "sha256": "5e788c8fbb22555ad9eff5dbc643cc7fd00ae48457c168ade480fb1884af006f", "sha256_in_prefix": "5e788c8fbb22555ad9eff5dbc643cc7fd00ae48457c168ade480fb1884af006f", "size_in_bytes": 686080}, {"_path": "Library/bin/libssl-1_1-x64.pdb", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\\\b\\\\abs_12_dc77dam\\\\croot\\\\openssl_1694464964882\\\\_h_env", "sha256": "f54b2188786f053325b6667ecd23495d3e4bdef65d7cde2aef5f02dd446f9aa1", "sha256_in_prefix": "f54b2188786f053325b6667ecd23495d3e4bdef65d7cde2aef5f02dd446f9aa1", "size_in_bytes": 2338816}, {"_path": "Library/bin/openssl.exe", "path_type": "hardlink", "sha256": "5c7c4f10fe27d5ec6d094b3e76d3502364728f5e7f9468cb327d6c9f3cae51fa", "sha256_in_prefix": "5c7c4f10fe27d5ec6d094b3e76d3502364728f5e7f9468cb327d6c9f3cae51fa", "size_in_bytes": 544256}, {"_path": "Library/bin/openssl.pdb", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\\\b\\\\abs_12_dc77dam\\\\croot\\\\openssl_1694464964882\\\\_h_env", "sha256": "46e531c05624e1260d22acc571c7f0c4da26fd3ad23a11c12f157da96298801c", "sha256_in_prefix": "46e531c05624e1260d22acc571c7f0c4da26fd3ad23a11c12f157da96298801c", "size_in_bytes": 2551808}, {"_path": "Library/include/openssl/__DECC_INCLUDE_EPILOGUE.H", "path_type": "hardlink", "sha256": "797eeebf0fefaf464c6b16b02883cae3bf629de7610d48e9c4ba5efeefd67842", "sha256_in_prefix": "797eeebf0fefaf464c6b16b02883cae3bf629de7610d48e9c4ba5efeefd67842", "size_in_bytes": 726}, {"_path": "Library/include/openssl/__DECC_INCLUDE_PROLOGUE.H", "path_type": "hardlink", "sha256": "57b996ee571c00c298204b878fae1506e594655c15b6b751ec26b292f9d58337", "sha256_in_prefix": "57b996ee571c00c298204b878fae1506e594655c15b6b751ec26b292f9d58337", "size_in_bytes": 798}, {"_path": "Library/include/openssl/aes.h", "path_type": "hardlink", "sha256": "f6ba27e87d97ea578f01540a8f84b7eab05d42c178badce712d0cc668d8a9981", "sha256_in_prefix": "f6ba27e87d97ea578f01540a8f84b7eab05d42c178badce712d0cc668d8a9981", "size_in_bytes": 3349}, {"_path": "Library/include/openssl/applink.c", "path_type": "hardlink", "sha256": "461a11e8c465d61de2357631e7e8367eca3da7f11ad744f490cb56a1ae80115d", "sha256_in_prefix": "461a11e8c465d61de2357631e7e8367eca3da7f11ad744f490cb56a1ae80115d", "size_in_bytes": 3508}, {"_path": "Library/include/openssl/asn1.h", "path_type": "hardlink", "sha256": "851313b1176baa3d6896aaeeebef56216ebdd38dcdabb39b3eca36b6645352bc", "sha256_in_prefix": "851313b1176baa3d6896aaeeebef56216ebdd38dcdabb39b3eca36b6645352bc", "size_in_bytes": 33627}, {"_path": "Library/include/openssl/asn1_mac.h", "path_type": "hardlink", "sha256": "db4883ed3aa74e07338001b31ec7e3e68546521c54bbdfe68c473b33f8511ca6", "sha256_in_prefix": "db4883ed3aa74e07338001b31ec7e3e68546521c54bbdfe68c473b33f8511ca6", "size_in_bytes": 395}, {"_path": "Library/include/openssl/asn1err.h", "path_type": "hardlink", "sha256": "b89b16216ddd35f028f5bfd3fe0db4f2ead6c9415e7749b7115b0393b427ef2f", "sha256_in_prefix": "b89b16216ddd35f028f5bfd3fe0db4f2ead6c9415e7749b7115b0393b427ef2f", "size_in_bytes": 14687}, {"_path": "Library/include/openssl/asn1t.h", "path_type": "hardlink", "sha256": "2702f569302248b8228e493177cf0a469f127f1ce5c76bc684bc90fbdfc45743", "sha256_in_prefix": "2702f569302248b8228e493177cf0a469f127f1ce5c76bc684bc90fbdfc45743", "size_in_bytes": 32940}, {"_path": "Library/include/openssl/async.h", "path_type": "hardlink", "sha256": "ef96c716cac393f2cd6da304af029155ecd9f021869cd5a4c341ebdf249036bc", "sha256_in_prefix": "ef96c716cac393f2cd6da304af029155ecd9f021869cd5a4c341ebdf249036bc", "size_in_bytes": 2398}, {"_path": "Library/include/openssl/asyncerr.h", "path_type": "hardlink", "sha256": "cacc9ca951aa523d3ed6b9df2366022633925a6729d30cd134a05d2ffe5fb156", "sha256_in_prefix": "cacc9ca951aa523d3ed6b9df2366022633925a6729d30cd134a05d2ffe5fb156", "size_in_bytes": 1326}, {"_path": "Library/include/openssl/bio.h", "path_type": "hardlink", "sha256": "78b055555957eec2bfb4ce5f91b27bdcfdf73128026be9ce85fcca0126fdcd3d", "sha256_in_prefix": "78b055555957eec2bfb4ce5f91b27bdcfdf73128026be9ce85fcca0126fdcd3d", "size_in_bytes": 34907}, {"_path": "Library/include/openssl/bioerr.h", "path_type": "hardlink", "sha256": "ad29fe4ef37fd7c4b256b690caf9371a74e29981f6561b94436dac54a61bf74b", "sha256_in_prefix": "ad29fe4ef37fd7c4b256b690caf9371a74e29981f6561b94436dac54a61bf74b", "size_in_bytes": 6400}, {"_path": "Library/include/openssl/blowfish.h", "path_type": "hardlink", "sha256": "813b2c7cff888b5b709b90d4da584afef59b5ce1ea888617d682f1af48578faf", "sha256_in_prefix": "813b2c7cff888b5b709b90d4da584afef59b5ce1ea888617d682f1af48578faf", "size_in_bytes": 1847}, {"_path": "Library/include/openssl/bn.h", "path_type": "hardlink", "sha256": "8f79b29c5ad479186acaae962a598d9868dc434fd5fa49a008eecfaff1b998ca", "sha256_in_prefix": "8f79b29c5ad479186acaae962a598d9868dc434fd5fa49a008eecfaff1b998ca", "size_in_bytes": 22135}, {"_path": "Library/include/openssl/bnerr.h", "path_type": "hardlink", "sha256": "8098ab9545307d6e2c9cc28ae995d6fda0e8959b6167cc03d67716b02abb27ff", "sha256_in_prefix": "8098ab9545307d6e2c9cc28ae995d6fda0e8959b6167cc03d67716b02abb27ff", "size_in_bytes": 4969}, {"_path": "Library/include/openssl/buffer.h", "path_type": "hardlink", "sha256": "a4fa292b903cb8c2ce1761ba4980cf5bdfb66dcadcbb5c8aecc45b012bc64c23", "sha256_in_prefix": "a4fa292b903cb8c2ce1761ba4980cf5bdfb66dcadcbb5c8aecc45b012bc64c23", "size_in_bytes": 1600}, {"_path": "Library/include/openssl/buffererr.h", "path_type": "hardlink", "sha256": "722f6ea87711b7c0cb6ee29c15762c2839e5ad17b9ef579b6e81b6121f035c30", "sha256_in_prefix": "722f6ea87711b7c0cb6ee29c15762c2839e5ad17b9ef579b6e81b6121f035c30", "size_in_bytes": 820}, {"_path": "Library/include/openssl/camellia.h", "path_type": "hardlink", "sha256": "561bdb2e985458e809e656c60e4bc83e1d6707746dd8b5badbc10b64198d4770", "sha256_in_prefix": "561bdb2e985458e809e656c60e4bc83e1d6707746dd8b5badbc10b64198d4770", "size_in_bytes": 3179}, {"_path": "Library/include/openssl/cast.h", "path_type": "hardlink", "sha256": "a14c51a129b3ae5795dafd98065be0bb0cbf00b4d4528e85adbf629966f42f53", "sha256_in_prefix": "a14c51a129b3ae5795dafd98065be0bb0cbf00b4d4528e85adbf629966f42f53", "size_in_bytes": 1674}, {"_path": "Library/include/openssl/cmac.h", "path_type": "hardlink", "sha256": "75bf95d1da118ff1e2f143ccb7a564d949de440097b97a49725c03976e167b52", "sha256_in_prefix": "75bf95d1da118ff1e2f143ccb7a564d949de440097b97a49725c03976e167b52", "size_in_bytes": 1064}, {"_path": "Library/include/openssl/cms.h", "path_type": "hardlink", "sha256": "07e4b0a779c957ef9026d69c0cb5a621ddc2060af967e0dd5e91ac861fdfbdb2", "sha256_in_prefix": "07e4b0a779c957ef9026d69c0cb5a621ddc2060af967e0dd5e91ac861fdfbdb2", "size_in_bytes": 16379}, {"_path": "Library/include/openssl/cmserr.h", "path_type": "hardlink", "sha256": "7e0fddaba85fb3d11262aee2c60272592dee1a6234a52a94b06bafd1c0a54350", "sha256_in_prefix": "7e0fddaba85fb3d11262aee2c60272592dee1a6234a52a94b06bafd1c0a54350", "size_in_bytes": 11223}, {"_path": "Library/include/openssl/comp.h", "path_type": "hardlink", "sha256": "98a877c62723e6042e4e4740cd1a6e7799df752d9234386d73a28a098e6eb31c", "sha256_in_prefix": "98a877c62723e6042e4e4740cd1a6e7799df752d9234386d73a28a098e6eb31c", "size_in_bytes": 1328}, {"_path": "Library/include/openssl/comperr.h", "path_type": "hardlink", "sha256": "f64be205d08af2557187ec19c03678aa9e29f66e428de29cebdd62cd7c3b5515", "sha256_in_prefix": "f64be205d08af2557187ec19c03678aa9e29f66e428de29cebdd62cd7c3b5515", "size_in_bytes": 1212}, {"_path": "Library/include/openssl/conf.h", "path_type": "hardlink", "sha256": "e54341e30523dadc09d821366f5afb13c9286d540b34c1945406918ed588fa21", "sha256_in_prefix": "e54341e30523dadc09d821366f5afb13c9286d540b34c1945406918ed588fa21", "size_in_bytes": 5601}, {"_path": "Library/include/openssl/conf_api.h", "path_type": "hardlink", "sha256": "175f86b69b58b0cd1da6c9b27d87ab4e4ed52f37dc76b7895c210c43469504eb", "sha256_in_prefix": "175f86b69b58b0cd1da6c9b27d87ab4e4ed52f37dc76b7895c210c43469504eb", "size_in_bytes": 1300}, {"_path": "Library/include/openssl/conferr.h", "path_type": "hardlink", "sha256": "abfe26485cd43a80e0c476e628979612ad28a635577baf68eee476850425a498", "sha256_in_prefix": "abfe26485cd43a80e0c476e628979612ad28a635577baf68eee476850425a498", "size_in_bytes": 3429}, {"_path": "Library/include/openssl/crypto.h", "path_type": "hardlink", "sha256": "d2cd84e695e68051fe67fa7cf901ccba4fadccc71a6b30090df20a01b91d3ec7", "sha256_in_prefix": "d2cd84e695e68051fe67fa7cf901ccba4fadccc71a6b30090df20a01b91d3ec7", "size_in_bytes": 17239}, {"_path": "Library/include/openssl/cryptoerr.h", "path_type": "hardlink", "sha256": "e752d7d3da32a6c009cf264450726367fd69e7c2a4c185d580ce650021d68e7a", "sha256_in_prefix": "e752d7d3da32a6c009cf264450726367fd69e7c2a4c185d580ce650021d68e7a", "size_in_bytes": 2261}, {"_path": "Library/include/openssl/ct.h", "path_type": "hardlink", "sha256": "b27aaf0d39609500b4fce3b3b65f6752116acb30420de1d53943074a481043f0", "sha256_in_prefix": "b27aaf0d39609500b4fce3b3b65f6752116acb30420de1d53943074a481043f0", "size_in_bytes": 15872}, {"_path": "Library/include/openssl/cterr.h", "path_type": "hardlink", "sha256": "ab6e70b52e341247c4b217883980ff1e98e4453d1020b0b038cdbf096c4ab8a5", "sha256_in_prefix": "ab6e70b52e341247c4b217883980ff1e98e4453d1020b0b038cdbf096c4ab8a5", "size_in_bytes": 3470}, {"_path": "Library/include/openssl/des.h", "path_type": "hardlink", "sha256": "3cb68fbb498e34e19e46f9d41561596ab69ccf78b1d8bdf2f312e892c060015f", "sha256_in_prefix": "3cb68fbb498e34e19e46f9d41561596ab69ccf78b1d8bdf2f312e892c060015f", "size_in_bytes": 7627}, {"_path": "Library/include/openssl/dh.h", "path_type": "hardlink", "sha256": "46d0c489e6b8063792cf9d8193f52738f0d8feca4eb84455e0a359636d41a9e0", "sha256_in_prefix": "46d0c489e6b8063792cf9d8193f52738f0d8feca4eb84455e0a359636d41a9e0", "size_in_bytes": 13505}, {"_path": "Library/include/openssl/dherr.h", "path_type": "hardlink", "sha256": "72990dc5ecf857be321c88b921502c853657f809ec53eaf91a011c099a85770d", "sha256_in_prefix": "72990dc5ecf857be321c88b921502c853657f809ec53eaf91a011c099a85770d", "size_in_bytes": 4037}, {"_path": "Library/include/openssl/dsa.h", "path_type": "hardlink", "sha256": "1525e5adf242643a7d36312580314f5ce61ecadf82223a49ff5c32220d78f9b9", "sha256_in_prefix": "1525e5adf242643a7d36312580314f5ce61ecadf82223a49ff5c32220d78f9b9", "size_in_bytes": 10051}, {"_path": "Library/include/openssl/dsaerr.h", "path_type": "hardlink", "sha256": "65ea8b47cb25ae82d611337cd47da7c1c5cdb9451982beec9d841413d4a923a9", "sha256_in_prefix": "65ea8b47cb25ae82d611337cd47da7c1c5cdb9451982beec9d841413d4a923a9", "size_in_bytes": 2972}, {"_path": "Library/include/openssl/dtls1.h", "path_type": "hardlink", "sha256": "7304f17cc9998bd7c16539ae9a5ea545f2e0c64086d5e688caba104b6a7ffecc", "sha256_in_prefix": "7304f17cc9998bd7c16539ae9a5ea545f2e0c64086d5e688caba104b6a7ffecc", "size_in_bytes": 1578}, {"_path": "Library/include/openssl/e_os2.h", "path_type": "hardlink", "sha256": "87c55abdb4755ffc04cd5e35a36d8b1ff2f19f3d8b9c7ef10357a78ff887503b", "sha256_in_prefix": "87c55abdb4755ffc04cd5e35a36d8b1ff2f19f3d8b9c7ef10357a78ff887503b", "size_in_bytes": 8920}, {"_path": "Library/include/openssl/ebcdic.h", "path_type": "hardlink", "sha256": "2289f14f11e75ac739d1123651d16e841f3c74e36daa483c1fed9f8c5c144720", "sha256_in_prefix": "2289f14f11e75ac739d1123651d16e841f3c74e36daa483c1fed9f8c5c144720", "size_in_bytes": 924}, {"_path": "Library/include/openssl/ec.h", "path_type": "hardlink", "sha256": "4e624064531986560789f8509dfd10bc5ed82eb65599e03ed3bfed57be6df199", "sha256_in_prefix": "4e624064531986560789f8509dfd10bc5ed82eb65599e03ed3bfed57be6df199", "size_in_bytes": 63684}, {"_path": "Library/include/openssl/ecdh.h", "path_type": "hardlink", "sha256": "95fb89add3bd32b6d43dcf1a51d1839b915e774d7138afaf618e690efbf414bb", "sha256_in_prefix": "95fb89add3bd32b6d43dcf1a51d1839b915e774d7138afaf618e690efbf414bb", "size_in_bytes": 358}, {"_path": "Library/include/openssl/ecdsa.h", "path_type": "hardlink", "sha256": "95fb89add3bd32b6d43dcf1a51d1839b915e774d7138afaf618e690efbf414bb", "sha256_in_prefix": "95fb89add3bd32b6d43dcf1a51d1839b915e774d7138afaf618e690efbf414bb", "size_in_bytes": 358}, {"_path": "Library/include/openssl/ecerr.h", "path_type": "hardlink", "sha256": "85a3b64c0644420a18295c8001a06bad5d7c3e84d4f871c925a1b45fb89ef182", "sha256_in_prefix": "85a3b64c0644420a18295c8001a06bad5d7c3e84d4f871c925a1b45fb89ef182", "size_in_bytes": 15821}, {"_path": "Library/include/openssl/engine.h", "path_type": "hardlink", "sha256": "a98df07aeac579293b186ea61c35edce3eebde1fd03d8553563f18023de47c95", "sha256_in_prefix": "a98df07aeac579293b186ea61c35edce3eebde1fd03d8553563f18023de47c95", "size_in_bytes": 34726}, {"_path": "Library/include/openssl/engineerr.h", "path_type": "hardlink", "sha256": "6f3d1e91ba1b7971929530cb8ac3ef934c0b82a8cd459caf224e241a3e5ad6e9", "sha256_in_prefix": "6f3d1e91ba1b7971929530cb8ac3ef934c0b82a8cd459caf224e241a3e5ad6e9", "size_in_bytes": 5447}, {"_path": "Library/include/openssl/err.h", "path_type": "hardlink", "sha256": "1447263f0a840e8135c586e16c3d858cee939deddf4fd905e391869809b4daa2", "sha256_in_prefix": "1447263f0a840e8135c586e16c3d858cee939deddf4fd905e391869809b4daa2", "size_in_bytes": 11269}, {"_path": "Library/include/openssl/evp.h", "path_type": "hardlink", "sha256": "9d963bdda81802988170663ccfcef437aea69042f42aef3a11ab4a217b800aef", "sha256_in_prefix": "9d963bdda81802988170663ccfcef437aea69042f42aef3a11ab4a217b800aef", "size_in_bytes": 76828}, {"_path": "Library/include/openssl/evperr.h", "path_type": "hardlink", "sha256": "45d68b462f25239488c020bee5fbf69700b0cd7b4dc2b3ba7015bddc499476a4", "sha256_in_prefix": "45d68b462f25239488c020bee5fbf69700b0cd7b4dc2b3ba7015bddc499476a4", "size_in_bytes": 11453}, {"_path": "Library/include/openssl/hmac.h", "path_type": "hardlink", "sha256": "76386c2273f105f54c5749f2fc854573c371ab185f9248ce295a748e126caae8", "sha256_in_prefix": "76386c2273f105f54c5749f2fc854573c371ab185f9248ce295a748e126caae8", "size_in_bytes": 1591}, {"_path": "Library/include/openssl/idea.h", "path_type": "hardlink", "sha256": "bab682c5cc06027c82d126ed8e65d7dd9dcc75fa464ccbc95d7f168bd69c732d", "sha256_in_prefix": "bab682c5cc06027c82d126ed8e65d7dd9dcc75fa464ccbc95d7f168bd69c732d", "size_in_bytes": 2099}, {"_path": "Library/include/openssl/kdf.h", "path_type": "hardlink", "sha256": "aaafd4a962ba9b9f2a2462076c7f7b4991d9d87e7bef71d87e9308125e0e29c0", "sha256_in_prefix": "aaafd4a962ba9b9f2a2462076c7f7b4991d9d87e7bef71d87e9308125e0e29c0", "size_in_bytes": 4326}, {"_path": "Library/include/openssl/kdferr.h", "path_type": "hardlink", "sha256": "e406cbc8a8a7956bcb0e06a949454b48d2702339caf4a2e621ac0cde9c2463e3", "sha256_in_prefix": "e406cbc8a8a7956bcb0e06a949454b48d2702339caf4a2e621ac0cde9c2463e3", "size_in_bytes": 2122}, {"_path": "Library/include/openssl/lhash.h", "path_type": "hardlink", "sha256": "1036e20aba00e0585b96b91a00ae7792dc12501160e117d1f824833de7fe3752", "sha256_in_prefix": "1036e20aba00e0585b96b91a00ae7792dc12501160e117d1f824833de7fe3752", "size_in_bytes": 9271}, {"_path": "Library/include/openssl/md2.h", "path_type": "hardlink", "sha256": "9e28f0a8d5e2297649af298f5e3209d32fe9486edb1793cc698a757c91270662", "sha256_in_prefix": "9e28f0a8d5e2297649af298f5e3209d32fe9486edb1793cc698a757c91270662", "size_in_bytes": 1054}, {"_path": "Library/include/openssl/md4.h", "path_type": "hardlink", "sha256": "03d3fc9dceec6c168d219eb9f404ca08b478d5521622834bc24b7d45945702e4", "sha256_in_prefix": "03d3fc9dceec6c168d219eb9f404ca08b478d5521622834bc24b7d45945702e4", "size_in_bytes": 1322}, {"_path": "Library/include/openssl/md5.h", "path_type": "hardlink", "sha256": "a0612a8f7e69e3bc166fc186ca44be1e5b1d020b5da8b5be021f73d48c270b82", "sha256_in_prefix": "a0612a8f7e69e3bc166fc186ca44be1e5b1d020b5da8b5be021f73d48c270b82", "size_in_bytes": 1320}, {"_path": "Library/include/openssl/mdc2.h", "path_type": "hardlink", "sha256": "f13b01ec09c45c2634673d1b9b1a79adbd6ec32bdff94287308e2bb27408e537", "sha256_in_prefix": "f13b01ec09c45c2634673d1b9b1a79adbd6ec32bdff94287308e2bb27408e537", "size_in_bytes": 1053}, {"_path": "Library/include/openssl/modes.h", "path_type": "hardlink", "sha256": "f4e527987e296a26fc1c06f4f896baa5f457b7e67f94957d176eed469b0fe602", "sha256_in_prefix": "f4e527987e296a26fc1c06f4f896baa5f457b7e67f94957d176eed469b0fe602", "size_in_bytes": 10478}, {"_path": "Library/include/openssl/obj_mac.h", "path_type": "hardlink", "sha256": "81ebca20cc370ccf72f3103d1231c6a42e064730bb22261a97d86ddd0e7bc08a", "sha256_in_prefix": "81ebca20cc370ccf72f3103d1231c6a42e064730bb22261a97d86ddd0e7bc08a", "size_in_bytes": 217522}, {"_path": "Library/include/openssl/objects.h", "path_type": "hardlink", "sha256": "4018d7c54e6f19c3230af063f4dbc8523f1450528a4af15a97a0a1ff3fb3ba38", "sha256_in_prefix": "4018d7c54e6f19c3230af063f4dbc8523f1450528a4af15a97a0a1ff3fb3ba38", "size_in_bytes": 6633}, {"_path": "Library/include/openssl/objectserr.h", "path_type": "hardlink", "sha256": "0ec7330c122ae2b3174df95ddecea65ff661a6152cd0192529dd1411ef3f62a1", "sha256_in_prefix": "0ec7330c122ae2b3174df95ddecea65ff661a6152cd0192529dd1411ef3f62a1", "size_in_bytes": 1316}, {"_path": "Library/include/openssl/ocsp.h", "path_type": "hardlink", "sha256": "cfbbc3434b56c4671080326aeb8c3d007dab7d2c3621d5146c11aba347158b80", "sha256_in_prefix": "cfbbc3434b56c4671080326aeb8c3d007dab7d2c3621d5146c11aba347158b80", "size_in_bytes": 15305}, {"_path": "Library/include/openssl/ocsperr.h", "path_type": "hardlink", "sha256": "c73a57b1919601ff8c04f2dc9c62dbd130ef2d3ae39bb0fcdf25bc9e6eaf327a", "sha256_in_prefix": "c73a57b1919601ff8c04f2dc9c62dbd130ef2d3ae39bb0fcdf25bc9e6eaf327a", "size_in_bytes": 3356}, {"_path": "Library/include/openssl/opensslconf.h", "path_type": "hardlink", "sha256": "1aeda78b5d9936c9402de225d173b785cbb059789baa1f2fa66eddd18f96662b", "sha256_in_prefix": "1aeda78b5d9936c9402de225d173b785cbb059789baa1f2fa66eddd18f96662b", "size_in_bytes": 4996}, {"_path": "Library/include/openssl/opensslv.h", "path_type": "hardlink", "sha256": "0ca12c2aba8bfbe24c4c06861e9d57b939c5e72d1c08f3798a442d698deb7599", "sha256_in_prefix": "0ca12c2aba8bfbe24c4c06861e9d57b939c5e72d1c08f3798a442d698deb7599", "size_in_bytes": 4102}, {"_path": "Library/include/openssl/ossl_typ.h", "path_type": "hardlink", "sha256": "46f541a1cc49bf08908ab3bde05e2683fc56ea268ca3e7f2e27628653ecb4a14", "sha256_in_prefix": "46f541a1cc49bf08908ab3bde05e2683fc56ea268ca3e7f2e27628653ecb4a14", "size_in_bytes": 6266}, {"_path": "Library/include/openssl/pem.h", "path_type": "hardlink", "sha256": "32d62036d0c35d03fe7f7ddaaffe3caeafcf984ec16e4db7ed19d3e202e9d7ba", "sha256_in_prefix": "32d62036d0c35d03fe7f7ddaaffe3caeafcf984ec16e4db7ed19d3e202e9d7ba", "size_in_bytes": 15468}, {"_path": "Library/include/openssl/pem2.h", "path_type": "hardlink", "sha256": "6833ee5712125d1ce8f7b52437a752e40c2f4793276859a8228c0de71d35c3e0", "sha256_in_prefix": "6833ee5712125d1ce8f7b52437a752e40c2f4793276859a8228c0de71d35c3e0", "size_in_bytes": 415}, {"_path": "Library/include/openssl/pemerr.h", "path_type": "hardlink", "sha256": "d91c6016f3c1ba70683b92fde55500ec334777defb6486c41b0a9c4e402aa295", "sha256_in_prefix": "d91c6016f3c1ba70683b92fde55500ec334777defb6486c41b0a9c4e402aa295", "size_in_bytes": 5222}, {"_path": "Library/include/openssl/pkcs12.h", "path_type": "hardlink", "sha256": "97c129c6c9a493d7e5d3af123d96040d87c4e54fc5e41aec450832cb32a634d6", "sha256_in_prefix": "97c129c6c9a493d7e5d3af123d96040d87c4e54fc5e41aec450832cb32a634d6", "size_in_bytes": 9871}, {"_path": "Library/include/openssl/pkcs12err.h", "path_type": "hardlink", "sha256": "b7340727a0480f351823b669f55cd3989c065d373c8cf6ff8745b8356f61d5ff", "sha256_in_prefix": "b7340727a0480f351823b669f55cd3989c065d373c8cf6ff8745b8356f61d5ff", "size_in_bytes": 3749}, {"_path": "Library/include/openssl/pkcs7.h", "path_type": "hardlink", "sha256": "cdc3505c9bb9168a6fad434dd9d9a49ad630b9ae9216bd665b11051e04a709bc", "sha256_in_prefix": "cdc3505c9bb9168a6fad434dd9d9a49ad630b9ae9216bd665b11051e04a709bc", "size_in_bytes": 11590}, {"_path": "Library/include/openssl/pkcs7err.h", "path_type": "hardlink", "sha256": "ecee11e9fbddab20f78a018009e6a2daf287ff5df00679298b137fe9996d9386", "sha256_in_prefix": "ecee11e9fbddab20f78a018009e6a2daf287ff5df00679298b137fe9996d9386", "size_in_bytes": 5110}, {"_path": "Library/include/openssl/rand.h", "path_type": "hardlink", "sha256": "5a91abcbdd14dcf2fc62761ccc58ee9407274ea558c292fa77ad625204ac2bd7", "sha256_in_prefix": "5a91abcbdd14dcf2fc62761ccc58ee9407274ea558c292fa77ad625204ac2bd7", "size_in_bytes": 2213}, {"_path": "Library/include/openssl/rand_drbg.h", "path_type": "hardlink", "sha256": "12379cc0a7f168cbff8e08828da72dc0e87773bc6c3bd14c4b57506339b61fa5", "sha256_in_prefix": "12379cc0a7f168cbff8e08828da72dc0e87773bc6c3bd14c4b57506339b61fa5", "size_in_bytes": 4763}, {"_path": "Library/include/openssl/randerr.h", "path_type": "hardlink", "sha256": "41fcebfb0767afa03f3de2732d3f0ce46cd2367ec34b2016cdb5c9e84a9a8994", "sha256_in_prefix": "41fcebfb0767afa03f3de2732d3f0ce46cd2367ec34b2016cdb5c9e84a9a8994", "size_in_bytes": 4633}, {"_path": "Library/include/openssl/rc2.h", "path_type": "hardlink", "sha256": "ec2b9196898bbc45ff2ab00204f93a6f20c974225510f29097ad69a6eeebcdfe", "sha256_in_prefix": "ec2b9196898bbc45ff2ab00204f93a6f20c974225510f29097ad69a6eeebcdfe", "size_in_bytes": 1534}, {"_path": "Library/include/openssl/rc4.h", "path_type": "hardlink", "sha256": "6fc023442f524349685d13d50854ad773b12b8c7a153d72d615ab27dd4a3d609", "sha256_in_prefix": "6fc023442f524349685d13d50854ad773b12b8c7a153d72d615ab27dd4a3d609", "size_in_bytes": 825}, {"_path": "Library/include/openssl/rc5.h", "path_type": "hardlink", "sha256": "c238954a1df23f52362d6e5fa78df2c7a5a2ad6ef9536e489f0a23295efa0cab", "sha256_in_prefix": "c238954a1df23f52362d6e5fa78df2c7a5a2ad6ef9536e489f0a23295efa0cab", "size_in_bytes": 1988}, {"_path": "Library/include/openssl/ripemd.h", "path_type": "hardlink", "sha256": "8c74b93c10a9e83abc17ced3a8021af7506f39a0fadab07b5db2d4faebcf68b6", "sha256_in_prefix": "8c74b93c10a9e83abc17ced3a8021af7506f39a0fadab07b5db2d4faebcf68b6", "size_in_bytes": 1243}, {"_path": "Library/include/openssl/rsa.h", "path_type": "hardlink", "sha256": "509c4db9081195cf6c9e5fd4683890aeb39509f2997a1989dd5a57cf43039bc2", "sha256_in_prefix": "509c4db9081195cf6c9e5fd4683890aeb39509f2997a1989dd5a57cf43039bc2", "size_in_bytes": 22202}, {"_path": "Library/include/openssl/rsaerr.h", "path_type": "hardlink", "sha256": "e032a933b1a4c13ec901d7561ad23ace01881b60cdcc86af0e4cb0a25a2a3252", "sha256_in_prefix": "e032a933b1a4c13ec901d7561ad23ace01881b60cdcc86af0e4cb0a25a2a3252", "size_in_bytes": 9075}, {"_path": "Library/include/openssl/safestack.h", "path_type": "hardlink", "sha256": "57665dabb37f4f0bd853539d93c64cb8adf37fd9552dc9fad215cc8f47a991c4", "sha256_in_prefix": "57665dabb37f4f0bd853539d93c64cb8adf37fd9552dc9fad215cc8f47a991c4", "size_in_bytes": 8139}, {"_path": "Library/include/openssl/seed.h", "path_type": "hardlink", "sha256": "6bf2373dfb10dc5cbc626cf2fe86b9b1c82373d799bdd6be13eedaf7d4540d55", "sha256_in_prefix": "6bf2373dfb10dc5cbc626cf2fe86b9b1c82373d799bdd6be13eedaf7d4540d55", "size_in_bytes": 3479}, {"_path": "Library/include/openssl/sha.h", "path_type": "hardlink", "sha256": "0bb6745481ac56b67f450d09033e813bf8f6a5f2025e90d5eb539eab1ad5e323", "sha256_in_prefix": "0bb6745481ac56b67f450d09033e813bf8f6a5f2025e90d5eb539eab1ad5e323", "size_in_bytes": 3831}, {"_path": "Library/include/openssl/srp.h", "path_type": "hardlink", "sha256": "82a08bf9a866dec1b7deb66b4077690cee0f6caf91eb00136c5eed4e8d943d06", "sha256_in_prefix": "82a08bf9a866dec1b7deb66b4077690cee0f6caf91eb00136c5eed4e8d943d06", "size_in_bytes": 3827}, {"_path": "Library/include/openssl/srtp.h", "path_type": "hardlink", "sha256": "e50e2dd5df6a0db219091cd1c6768a6d319ef6485b16e1f361fce43067847626", "sha256_in_prefix": "e50e2dd5df6a0db219091cd1c6768a6d319ef6485b16e1f361fce43067847626", "size_in_bytes": 1316}, {"_path": "Library/include/openssl/ssl.h", "path_type": "hardlink", "sha256": "67e8553fa3a5a723f3b0f29f7f6dd51ff75b4a3d4e799eb1d98ef5d1af609e52", "sha256_in_prefix": "67e8553fa3a5a723f3b0f29f7f6dd51ff75b4a3d4e799eb1d98ef5d1af609e52", "size_in_bytes": 111816}, {"_path": "Library/include/openssl/ssl2.h", "path_type": "hardlink", "sha256": "7fb557a32488ad44a25420abff8279abd0bd1f4ab768e73d3e1d5c2dab36c0c5", "sha256_in_prefix": "7fb557a32488ad44a25420abff8279abd0bd1f4ab768e73d3e1d5c2dab36c0c5", "size_in_bytes": 542}, {"_path": "Library/include/openssl/ssl3.h", "path_type": "hardlink", "sha256": "d04cfec2a9f9da2aa299f55884215e200b490a6e0a9423255262648bd8a6d1c0", "sha256_in_prefix": "d04cfec2a9f9da2aa299f55884215e200b490a6e0a9423255262648bd8a6d1c0", "size_in_bytes": 14705}, {"_path": "Library/include/openssl/sslerr.h", "path_type": "hardlink", "sha256": "54a3c784ebfab631d21bb9b7db3ce38167acc0299e8920b5ce6e419a3736930a", "sha256_in_prefix": "54a3c784ebfab631d21bb9b7db3ce38167acc0299e8920b5ce6e419a3736930a", "size_in_bytes": 46862}, {"_path": "Library/include/openssl/stack.h", "path_type": "hardlink", "sha256": "45ba803bbe14007e494bb24b2ca954f6362ef700a10480efa3d2f3acb159deec", "sha256_in_prefix": "45ba803bbe14007e494bb24b2ca954f6362ef700a10480efa3d2f3acb159deec", "size_in_bytes": 3095}, {"_path": "Library/include/openssl/store.h", "path_type": "hardlink", "sha256": "1105bad1a309d3122a2cfbdc4098a33e33d50c8118e70bb332f3b7d6ef2bbb58", "sha256_in_prefix": "1105bad1a309d3122a2cfbdc4098a33e33d50c8118e70bb332f3b7d6ef2bbb58", "size_in_bytes": 11199}, {"_path": "Library/include/openssl/storeerr.h", "path_type": "hardlink", "sha256": "071c66bcc03ed5c2a24f1964f45e8d1a633f3cb4b183c718ded3e25312f8c4e0", "sha256_in_prefix": "071c66bcc03ed5c2a24f1964f45e8d1a633f3cb4b183c718ded3e25312f8c4e0", "size_in_bytes": 4399}, {"_path": "Library/include/openssl/symhacks.h", "path_type": "hardlink", "sha256": "0c9e026a5932a2432e3cdf7defb789610c4272010e51e5ff0471809eabb7aa2d", "sha256_in_prefix": "0c9e026a5932a2432e3cdf7defb789610c4272010e51e5ff0471809eabb7aa2d", "size_in_bytes": 1311}, {"_path": "Library/include/openssl/tls1.h", "path_type": "hardlink", "sha256": "d4104ca8720332b9852f4725d4660a6cf77a52b587e7d96ec263e996c1d0ae2f", "sha256_in_prefix": "d4104ca8720332b9852f4725d4660a6cf77a52b587e7d96ec263e996c1d0ae2f", "size_in_bytes": 72490}, {"_path": "Library/include/openssl/ts.h", "path_type": "hardlink", "sha256": "9bd039ebce7bf6b6e71fc9667e44e017fc0cb7c79c023be1c965894e61b79238", "sha256_in_prefix": "9bd039ebce7bf6b6e71fc9667e44e017fc0cb7c79c023be1c965894e61b79238", "size_in_bytes": 22429}, {"_path": "Library/include/openssl/tserr.h", "path_type": "hardlink", "sha256": "b6e1bbc8c53e4f7c054768dec55272d001dfbfee788a85ba8b0c069e08cbbe85", "sha256_in_prefix": "b6e1bbc8c53e4f7c054768dec55272d001dfbfee788a85ba8b0c069e08cbbe85", "size_in_bytes": 6746}, {"_path": "Library/include/openssl/txt_db.h", "path_type": "hardlink", "sha256": "903696bd5b9908530f8a8578fef47721f47f3fe8507bac7761473e925e942159", "sha256_in_prefix": "903696bd5b9908530f8a8578fef47721f47f3fe8507bac7761473e925e942159", "size_in_bytes": 1666}, {"_path": "Library/include/openssl/ui.h", "path_type": "hardlink", "sha256": "f5ea3f5d2e7beb3e81db8c23c6e618e38e511c213fd93a11ef51b9ae4ad03619", "sha256_in_prefix": "f5ea3f5d2e7beb3e81db8c23c6e618e38e511c213fd93a11ef51b9ae4ad03619", "size_in_bytes": 16052}, {"_path": "Library/include/openssl/uierr.h", "path_type": "hardlink", "sha256": "bb39377c702d9765547ce95f9e758b46a54ee4e2689f892033cf2a4ea57d5d0b", "sha256_in_prefix": "bb39377c702d9765547ce95f9e758b46a54ee4e2689f892033cf2a4ea57d5d0b", "size_in_bytes": 2737}, {"_path": "Library/include/openssl/whrlpool.h", "path_type": "hardlink", "sha256": "38750722d1737083fa8caedfccd3bce574080e35692010d2f91dd303b154af69", "sha256_in_prefix": "38750722d1737083fa8caedfccd3bce574080e35692010d2f91dd303b154af69", "size_in_bytes": 1377}, {"_path": "Library/include/openssl/x509.h", "path_type": "hardlink", "sha256": "f1b8b356d649da24236d5aabdfafe047b516b2008d8d5d09da5e99fbc4b4a98c", "sha256_in_prefix": "f1b8b356d649da24236d5aabdfafe047b516b2008d8d5d09da5e99fbc4b4a98c", "size_in_bytes": 43326}, {"_path": "Library/include/openssl/x509_vfy.h", "path_type": "hardlink", "sha256": "f621ac4c38a89bd010656ca00937773126a7ef348b29a14b04cc5ec5c095b531", "sha256_in_prefix": "f621ac4c38a89bd010656ca00937773126a7ef348b29a14b04cc5ec5c095b531", "size_in_bytes": 32451}, {"_path": "Library/include/openssl/x509err.h", "path_type": "hardlink", "sha256": "622499b9aaa546957c802da9aee36794c24c9c7d2e105ae69b47841ecdac6b7a", "sha256_in_prefix": "622499b9aaa546957c802da9aee36794c24c9c7d2e105ae69b47841ecdac6b7a", "size_in_bytes": 6803}, {"_path": "Library/include/openssl/x509v3.h", "path_type": "hardlink", "sha256": "a7e78a1858697e75940b416ba1fb6f4e135722e88c0c280af8a979975c5443da", "sha256_in_prefix": "a7e78a1858697e75940b416ba1fb6f4e135722e88c0c280af8a979975c5443da", "size_in_bytes": 33441}, {"_path": "Library/include/openssl/x509v3err.h", "path_type": "hardlink", "sha256": "3d17dcca277cd6f6373a30a6e07e1a2c44024a0b980ccccc2533d78bf0c22776", "sha256_in_prefix": "3d17dcca277cd6f6373a30a6e07e1a2c44024a0b980ccccc2533d78bf0c22776", "size_in_bytes": 8901}, {"_path": "Library/lib/engines-1_1/capi.dll", "path_type": "hardlink", "sha256": "606afee99810ccdf8c04e18a500018304a9f53742a10ec25c1e3598d852e5c7a", "sha256_in_prefix": "606afee99810ccdf8c04e18a500018304a9f53742a10ec25c1e3598d852e5c7a", "size_in_bytes": 69632}, {"_path": "Library/lib/engines-1_1/capi.pdb", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\b\\abs_12_dc77dam\\croot\\openssl_1694464964882\\_h_env", "sha256": "ffb7e8c6426c387fb3747235e0063d20161eb338f2f039257c6239dbbde6006e", "sha256_in_prefix": "ffb7e8c6426c387fb3747235e0063d20161eb338f2f039257c6239dbbde6006e", "size_in_bytes": 528384}, {"_path": "Library/lib/engines-1_1/padlock.dll", "path_type": "hardlink", "sha256": "4b3d353596bc9c9c745dad28ab479ac11cb484a8de0b71a87b806aebfb8006cd", "sha256_in_prefix": "4b3d353596bc9c9c745dad28ab479ac11cb484a8de0b71a87b806aebfb8006cd", "size_in_bytes": 39936}, {"_path": "Library/lib/engines-1_1/padlock.pdb", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\b\\abs_12_dc77dam\\croot\\openssl_1694464964882\\_h_env", "sha256": "d30f51debb5404af8d22537a06f7bcd84dd6f2c84a9bdf087e2afa18dda5eb5f", "sha256_in_prefix": "d30f51debb5404af8d22537a06f7bcd84dd6f2c84a9bdf087e2afa18dda5eb5f", "size_in_bytes": 348160}, {"_path": "Library/lib/libcrypto.lib", "path_type": "hardlink", "sha256": "acc5720991dfc67a5592c906a4d2284e68e4fc2a0dc3ee8994094ce404510bb2", "sha256_in_prefix": "acc5720991dfc67a5592c906a4d2284e68e4fc2a0dc3ee8994094ce404510bb2", "size_in_bytes": 1011470}, {"_path": "Library/lib/libssl.lib", "path_type": "hardlink", "sha256": "b78f83f94c0b6bbe83184ca129cd064952b58ceae8599de845bc0ea725231e42", "sha256_in_prefix": "b78f83f94c0b6bbe83184ca129cd064952b58ceae8599de845bc0ea725231e42", "size_in_bytes": 121986}, {"_path": "Library/ssl/ct_log_list.cnf", "path_type": "hardlink", "sha256": "f1c1803d13d1d0b755b13b23c28bd4e20e07baf9f2b744c9337ba5866aa0ec3b", "sha256_in_prefix": "f1c1803d13d1d0b755b13b23c28bd4e20e07baf9f2b744c9337ba5866aa0ec3b", "size_in_bytes": 412}, {"_path": "Library/ssl/ct_log_list.cnf.dist", "path_type": "hardlink", "sha256": "f1c1803d13d1d0b755b13b23c28bd4e20e07baf9f2b744c9337ba5866aa0ec3b", "sha256_in_prefix": "f1c1803d13d1d0b755b13b23c28bd4e20e07baf9f2b744c9337ba5866aa0ec3b", "size_in_bytes": 412}, {"_path": "Library/ssl/misc/CA.pl", "path_type": "hardlink", "sha256": "557cc463c5e6fe521b5e09836c7287ec305092044cc4cb0c43e723d94628ade4", "sha256_in_prefix": "557cc463c5e6fe521b5e09836c7287ec305092044cc4cb0c43e723d94628ade4", "size_in_bytes": 7871}, {"_path": "Library/ssl/misc/tsget.pl", "path_type": "hardlink", "sha256": "342c444e859a66eaa2533ecd28336508a0c6faf4c3c9de42174e1a0372d8fb3f", "sha256_in_prefix": "342c444e859a66eaa2533ecd28336508a0c6faf4c3c9de42174e1a0372d8fb3f", "size_in_bytes": 6837}, {"_path": "Library/ssl/openssl.cnf", "path_type": "hardlink", "sha256": "f10ba64917b4458fafc1e078c2eb9e6a7602e68fc98c2e9e6df5e1636ae27d6b", "sha256_in_prefix": "f10ba64917b4458fafc1e078c2eb9e6a7602e68fc98c2e9e6df5e1636ae27d6b", "size_in_bytes": 10909}, {"_path": "Library/ssl/openssl.cnf.dist", "path_type": "hardlink", "sha256": "f10ba64917b4458fafc1e078c2eb9e6a7602e68fc98c2e9e6df5e1636ae27d6b", "sha256_in_prefix": "f10ba64917b4458fafc1e078c2eb9e6a7602e68fc98c2e9e6df5e1636ae27d6b", "size_in_bytes": 10909}], "paths_version": 1}, "requested_spec": "None", "sha256": "6a199ee325b4017701b27f90620daa6666c74b2f3ba9b9f8c79844b3fff15472", "size": 5763830, "subdir": "win-64", "timestamp": 1694466133007, "track_features": "", "url": "https://repo.anaconda.com/pkgs/main/win-64/openssl-1.1.1w-h2bbff1b_0.conda", "version": "1.1.1w"}